import subprocess
import platform

def test_command():
    """Test subprocess with proper Windows command handling"""

    print("=== Testing Subprocess Commands ===")

    try:
        if platform.system().lower() == "windows":
            # Test simple echo command through cmd
            print("Testing echo command...")
            result = subprocess.run(
                ["cmd", "/c", "echo", "Hello from Windows!"],
                capture_output=True,
                text=True,
                timeout=10
            )
            print(f"Echo result: {result.stdout.strip()}")
            print(f"Echo return code: {result.returncode}")

            # Test directory listing
            print("\nTesting directory listing...")
            result = subprocess.run(
                ["cmd", "/c", "dir", "/b"],
                capture_output=True,
                text=True,
                timeout=10
            )
            print(f"Directory listing (first 5 files):")
            files = result.stdout.strip().split('\n')[:5]
            for file in files:
                if file.strip():
                    print(f"  - {file.strip()}")

            # Test ping with localhost (should work even with network restrictions)
            print("\nTesting ping to localhost...")
            result = subprocess.run(
                ["ping", "-n", "2", "127.0.0.1"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                print("✅ Localhost ping successful!")
                # Show just the summary line
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'Packets:' in line:
                        print(f"  {line.strip()}")
            else:
                print("❌ Localhost ping failed")

            # Test ping to specific IP address
            target_ip = "************"
            print(f"\nTesting ping to {target_ip}...")
            result = subprocess.run(
                ["ping", "-n", "4", target_ip],
                capture_output=True,
                text=True,
                timeout=15
            )

            if result.returncode == 0:
                print(f"✅ {target_ip} ping successful!")
                # Show just the summary line
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'Packets:' in line:
                        print(f"  {line.strip()}")
            else:
                print(f"❌ {target_ip} ping failed")
                print(f"Return code: {result.returncode}")

            # Test telnet connectivity to common ports
            print(f"\nTesting connectivity to {target_ip} on common ports...")
            common_ports = [22, 80, 443, 3389, 8080]

            for port in common_ports:
                try:
                    result = subprocess.run(
                        ["powershell", "-Command", f"Test-NetConnection -ComputerName {target_ip} -Port {port} -InformationLevel Quiet"],
                        capture_output=True,
                        text=True,
                        timeout=5
                    )

                    if result.stdout.strip().lower() == "true":
                        print(f"  ✅ Port {port} - Open")
                    else:
                        print(f"  ❌ Port {port} - Closed/Filtered")

                except Exception as e:
                    print(f"  💥 Port {port} - Error: {e}")

        else:
            # Unix/Linux commands
            print("Testing Unix commands...")
            subprocess.run(["echo", "Hello from Unix!"])
            subprocess.run(["ping", "-c", "2", "127.0.0.1"])

    except subprocess.TimeoutExpired:
        print("⏰ Command timed out")
    except Exception as e:
        print(f"💥 Error: {e}")

if __name__ == "__main__":
    test_command()

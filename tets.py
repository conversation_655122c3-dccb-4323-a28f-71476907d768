#!/usr/bin/env python3
"""
daily_process.py

- Looks for expected files in UPLOAD_DIR (where files are dropped automatically).
- Renames files according to templates below (configurable).
- Runs the sequence of commands used by your daily process.

Usage:
    python3 daily_process.py        # runs, renames, executes commands
    python3 daily_process.py --dry  # shows actions without performing them
"""

import os
import shutil
import datetime
import glob
import subprocess
import argparse
from typing import Dict

# ===========================
# CONFIG - Edit these values
# ===========================
UPLOAD_DIR = "/app/tcs/Application/Exec/ExcgFtp"   # folder where files are dropped
RUN_DIR = "/app/tcs/Application/Exec/Run"          # folder to execute commands from
TODAY = datetime.datetime.now()
YYYYMMDD = TODAY.strftime("%Y%m%d")
DD_MON_YYYY = TODAY.strftime("%d-%b-%Y")           # e.g. 23-Aug-2024
BATCH = "23"   # document examples show a ".23" or "_23" part in DAT names; change if needed

# Mapping rules:
# key = glob pattern to match incoming file(s) in UPLOAD_DIR
# value = target filename template (use placeholders {YYYYMMDD}, {DDMONYYYY}, {BATCH})
#
# if you want "keep as is", omit it from mapping and the script will leave it alone.
RENAME_TEMPLATES: Dict[str, str] = {
    # rename fo_secban_23082024.csv -> fo_secban_23-Aug-2024.csv
    "fo_secban_*.csv": "fo_secban_{DDMONYYYY}.csv",

    # BSE span rename example: BSERISK20240823-00.spn -> Nsocl.20240823.i01.spn
    "BSERISK*-00.spn": "Nsocl.{YYYYMMDD}.i01.spn",

    # Common text files -> DAT patterns used by doc examples
    "Security.txt": "NSM{YYYYMMDD}.{BATCH}.DAT",     # doc example shows NSMload*.23.DAT
    "Contract.txt": "DNSM{YYYYMMDD}.{BATCH}.DAT",
    "Spd_contract.txt": "SCM{YYYYMMDD}.{BATCH}.DAT",
    "SCRIP_*.TXT": "BSM{YYYYMMDD}.{BATCH}.DAT",

    # REG files
    # - REG_IND220824.csv -> use "as is" for NSE (so we don't map it)
    # - REG_IND22-0824.csv -> rename for BSE
    "REG_IND22-0824.csv": "REG_IND22-{YYYYMMDD}_{BATCH}.DAT",

    # default fallback if you want to rename cd_contract (here left as-is)
    # "cd_contract.txt": "cd_contract_{YYYYMMDD}.txt",
}

# Commands to run in sequence (the document's commands)
COMMANDS = [
    f"cd {RUN_DIR} && ./NSMload {YYYYMMDD}",
    f"cd {RUN_DIR} && ./BulkDNSMload {YYYYMMDD}",
    f"cd {RUN_DIR} && ./SCMload {YYYYMMDD}",
    f"cd {RUN_DIR} && ./Scrip_Master {YYYYMMDD}",
    f"cd {RUN_DIR} && ./NseRegIndicator {YYYYMMDD}",
    f"cd {RUN_DIR} && ./BseRegIndicator {YYYYMMDD}",
]

# ===========================
# INTERNAL - do not edit below
# ===========================
def make_target_name(tpl: str):
    return tpl.format(YYYYMMDD=YYYYMMDD, DDMONYYYY=DD_MON_YYYY, DDMONYYYY_UPPER=DD_MON_YYYY.upper(), BATCH=BATCH)

def find_matches(pattern: str):
    glob_path = os.path.join(UPLOAD_DIR, pattern)
    return sorted(glob.glob(glob_path))

def safe_move(src, dst, dry_run=False):
    if dry_run:
        print(f"[DRY] would move: {src} -> {dst}")
        return
    # remove existing dst if present (doc examples sometimes overwrite)
    if os.path.exists(dst):
        backup = dst + ".bak"
        print(f" - target exists: backing up {dst} -> {backup}")
        shutil.move(dst, backup)
    print(f" - moving {src} -> {dst}")
    shutil.move(src, dst)

def process_renames(dry_run=False):
    print("Checking upload directory:", UPLOAD_DIR)
    for pattern, tpl in RENAME_TEMPLATES.items():
        matches = find_matches(pattern)
        if not matches:
            print(f"Pattern '{pattern}': no files found.")
            continue
        for src_path in matches:
            filename = os.path.basename(src_path)
            target_name = make_target_name(tpl)
            dst_path = os.path.join(UPLOAD_DIR, target_name)
            if filename == target_name:
                print(f" - '{filename}' already named correctly, skipping.")
                continue
            print(f"Pattern '{pattern}': rename '{filename}' -> '{target_name}'")
            safe_move(src_path, dst_path, dry_run=dry_run)

def run_command_list(dry_run=False):
    for cmd in COMMANDS:
        print("\n>>> Running:", cmd)
        if dry_run:
            print("[DRY] would run command, skipping execution.")
            continue
        r = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        print("STDOUT:")
        print(r.stdout.strip())
        if r.stderr:
            print("STDERR:")
            print(r.stderr.strip())

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--dry", action="store_true", help="dry-run (show actions, don't perform)")
    args = parser.parse_args()
    dry_run = args.dry

    process_renames(dry_run=dry_run)
    print("\nAll renames processed. Now executing commands.")
    run_command_list(dry_run=dry_run)
    print("\nDone.")

if __name__ == "__main__":
    main()

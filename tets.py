import subprocess
import platform

def test_command():
    """Test subprocess with proper Windows command handling"""

    print("=== Testing Subprocess Commands ===")

    try:
        if platform.system().lower() == "windows":
            # Test simple echo command through cmd
            print("Testing echo command...")
            result = subprocess.run(
                ["cmd", "/c", "echo", "Hello from Windows!"],
                capture_output=True,
                text=True,
                timeout=10
            )
            print(f"Echo result: {result.stdout.strip()}")
            print(f"Echo return code: {result.returncode}")

            # Test directory listing
            print("\nTesting directory listing...")
            result = subprocess.run(
                ["cmd", "/c", "dir", "/b"],
                capture_output=True,
                text=True,
                timeout=10
            )
            print(f"Directory listing (first 5 files):")
            files = result.stdout.strip().split('\n')[:5]
            for file in files:
                if file.strip():
                    print(f"  - {file.strip()}")

            # Test ping with localhost (should work even with network restrictions)
            print("\nTesting ping to localhost...")
            result = subprocess.run(
                ["ping", "-n", "2", "127.0.0.1"],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                print("✅ Localhost ping successful!")
                # Show just the summary line
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'Packets:' in line:
                        print(f"  {line.strip()}")
            else:
                print("❌ Localhost ping failed")

        else:
            # Unix/Linux commands
            print("Testing Unix commands...")
            subprocess.run(["echo", "Hello from Unix!"])
            subprocess.run(["ping", "-c", "2", "127.0.0.1"])

    except subprocess.TimeoutExpired:
        print("⏰ Command timed out")
    except Exception as e:
        print(f"💥 Error: {e}")

if __name__ == "__main__":
    test_command()

#!/usr/bin/env python3
"""
BSE SCRIP Download Scheduler
Automated SOD and EOD routines with scheduling
"""

import time
import schedule
from datetime import datetime
import logging
from bse_scrip_downloader import BSEScripDownloader

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scheduler.log'),
        logging.StreamHandler()
    ]
)

class BSEScheduler:
    def __init__(self):
        self.downloader = BSEScripDownloader()
    
    def sod_job(self):
        """Start of Day job"""
        logging.info("🌅 Starting SOD (Start of Day) routine")
        try:
            result = self.downloader.sod_routine()
            if result['success']:
                logging.info("✅ SOD routine completed successfully")
            else:
                logging.error("❌ SOD routine failed")
        except Exception as e:
            logging.error(f"💥 SOD routine error: {e}")
    
    def eod_job(self):
        """End of Day job"""
        logging.info("🌆 Starting EOD (End of Day) routine")
        try:
            result = self.downloader.eod_routine()
            if result['success']:
                logging.info("✅ EOD routine completed successfully")
            else:
                logging.error("❌ EOD routine failed")
        except Exception as e:
            logging.error(f"💥 EOD routine error: {e}")
    
    def run_scheduler(self):
        """Run the scheduler"""
        # Schedule SOD at 9:00 AM
        schedule.every().day.at("09:00").do(self.sod_job)
        
        # Schedule EOD at 6:00 PM
        schedule.every().day.at("18:00").do(self.eod_job)
        
        logging.info("📅 Scheduler started")
        logging.info("⏰ SOD scheduled for 09:00 daily")
        logging.info("⏰ EOD scheduled for 18:00 daily")
        
        while True:
            schedule.run_pending()
            time.sleep(60)  # Check every minute

def main():
    """Main function"""
    print("BSE SCRIP Download Scheduler")
    print("=" * 30)
    print("1. Run scheduler (SOD at 9:00 AM, EOD at 6:00 PM)")
    print("2. Run SOD now")
    print("3. Run EOD now")
    print("4. Exit")
    
    choice = input("Enter your choice (1-4): ").strip()
    
    scheduler = BSEScheduler()
    
    if choice == "1":
        print("Starting scheduler... Press Ctrl+C to stop")
        try:
            scheduler.run_scheduler()
        except KeyboardInterrupt:
            print("\n👋 Scheduler stopped")
    elif choice == "2":
        scheduler.sod_job()
    elif choice == "3":
        scheduler.eod_job()
    elif choice == "4":
        print("👋 Goodbye!")
    else:
        print("❌ Invalid choice")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
System Test Script - Tests various system functions
"""

import subprocess
import platform
import sys
import os
from datetime import datetime

def test_system_info():
    """Test system information"""
    print("=== System Information ===")
    print(f"Platform: {platform.system()}")
    print(f"Platform Release: {platform.release()}")
    print(f"Platform Version: {platform.version()}")
    print(f"Architecture: {platform.architecture()}")
    print(f"Machine: {platform.machine()}")
    print(f"Python Version: {sys.version}")
    print(f"Current Directory: {os.getcwd()}")
    print(f"Current Time: {datetime.now()}")
    print()

def test_network_connectivity():
    """Test network connectivity"""
    print("=== Network Connectivity Test ===")
    
    # Test different hosts
    hosts = ["*******", "*******", "google.com"]
    
    for host in hosts:
        print(f"Testing connectivity to {host}...")
        
        try:
            if platform.system().lower() == "windows":
                # Windows ping command
                result = subprocess.run(
                    ["ping", "-n", "2", host], 
                    capture_output=True, 
                    text=True, 
                    timeout=10
                )
            else:
                # Unix/Linux/Mac ping command
                result = subprocess.run(
                    ["ping", "-c", "2", host], 
                    capture_output=True, 
                    text=True, 
                    timeout=10
                )
            
            if result.returncode == 0:
                print(f"  ✅ {host} - Reachable")
            else:
                print(f"  ❌ {host} - Unreachable (return code: {result.returncode})")
                
        except subprocess.TimeoutExpired:
            print(f"  ⏰ {host} - Timeout")
        except Exception as e:
            print(f"  💥 {host} - Error: {e}")
    
    print()

def test_file_operations():
    """Test file operations"""
    print("=== File Operations Test ===")
    
    test_file = "test_temp.txt"
    
    try:
        # Write test
        with open(test_file, 'w') as f:
            f.write("Test file content\n")
            f.write(f"Created at: {datetime.now()}\n")
        print(f"  ✅ File write successful: {test_file}")
        
        # Read test
        with open(test_file, 'r') as f:
            content = f.read()
        print(f"  ✅ File read successful")
        
        # Delete test
        os.remove(test_file)
        print(f"  ✅ File delete successful")
        
    except Exception as e:
        print(f"  ❌ File operations error: {e}")
    
    print()

def test_subprocess():
    """Test subprocess functionality"""
    print("=== Subprocess Test ===")
    
    try:
        if platform.system().lower() == "windows":
            # Test Windows commands
            commands = [
                ["echo", "Hello World"],
                ["dir", "/b"],
                ["python", "--version"]
            ]
        else:
            # Test Unix commands
            commands = [
                ["echo", "Hello World"],
                ["ls", "-la"],
                ["python3", "--version"]
            ]
        
        for cmd in commands:
            try:
                result = subprocess.run(
                    cmd, 
                    capture_output=True, 
                    text=True, 
                    timeout=5
                )
                
                if result.returncode == 0:
                    print(f"  ✅ Command '{' '.join(cmd)}' - Success")
                    if result.stdout.strip():
                        print(f"     Output: {result.stdout.strip()[:100]}...")
                else:
                    print(f"  ❌ Command '{' '.join(cmd)}' - Failed (code: {result.returncode})")
                    
            except Exception as e:
                print(f"  💥 Command '{' '.join(cmd)}' - Error: {e}")
    
    except Exception as e:
        print(f"  💥 Subprocess test error: {e}")
    
    print()

def main():
    """Main test function"""
    print("Python System Test Suite")
    print("=" * 50)
    print()
    
    test_system_info()
    test_file_operations()
    test_subprocess()
    test_network_connectivity()
    
    print("=" * 50)
    print("Test suite completed!")

if __name__ == "__main__":
    main()

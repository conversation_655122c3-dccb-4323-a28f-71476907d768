#!/usr/bin/env python3
"""
Local Go Command Testing
Test Go commands locally before running on server
"""

import subprocess
import os
from datetime import datetime

def run_command(cmd, description=""):
    """Run any command locally"""
    print(f"🚀 {description}")
    print(f"Command: {' '.join(cmd) if isinstance(cmd, list) else cmd}")
    print("-" * 50)
    
    try:
        if isinstance(cmd, str):
            # For string commands, use shell=True
            result = subprocess.run(
                cmd,
                shell=True,
                capture_output=True,
                text=True,
                timeout=30
            )
        else:
            # For list commands
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=30
            )
        
        print(f"Return Code: {result.returncode}")
        
        if result.stdout:
            print("Output:")
            print(result.stdout)
        
        if result.stderr:
            print("Errors:")
            print(result.stderr)
        
        print("=" * 50)
        return result.returncode == 0
        
    except FileNotFoundError:
        print(f"❌ Command not found: {cmd[0] if isinstance(cmd, list) else cmd.split()[0]}")
        print("=" * 50)
        return False
    except subprocess.TimeoutExpired:
        print("⏰ Command timed out")
        print("=" * 50)
        return False
    except Exception as e:
        print(f"💥 Error: {e}")
        print("=" * 50)
        return False

def test_go_commands():
    """Test various Go commands locally"""
    print("Testing Go Commands Locally")
    print("=" * 50)
    
    # Test if Go is available
    go_available = run_command(["go", "version"], "Checking Go version")
    
    if not go_available:
        print("❌ Go is not installed. Install Go first:")
        print("   - Download from: https://go.dev/dl/")
        print("   - Or use: winget install GoLang.Go")
        return False
    
    # Test Go environment
    run_command(["go", "env"], "Checking Go environment")
    
    # Test Go help
    run_command(["go", "help"], "Getting Go help")
    
    return True

def test_system_commands():
    """Test basic system commands"""
    print("Testing System Commands")
    print("=" * 50)
    
    # Test basic commands
    commands = [
        (["echo", "Hello World"], "Testing echo"),
        (["python", "--version"], "Testing Python"),
        ("dir", "Testing directory listing"),
        ("whoami", "Testing current user"),
        ("hostname", "Testing hostname"),
    ]
    
    for cmd, desc in commands:
        run_command(cmd, desc)

def create_sample_go_program():
    """Create a sample Go program for testing"""
    go_code = '''package main

import "fmt"

func main() {
    fmt.Println("Hello from Go!")
    fmt.Println("This is a test program")
}
'''
    
    try:
        with open("hello.go", "w") as f:
            f.write(go_code)
        print("✅ Created hello.go")
        return True
    except Exception as e:
        print(f"❌ Failed to create hello.go: {e}")
        return False

def test_go_program():
    """Test running a Go program"""
    print("Testing Go Program Execution")
    print("=" * 50)
    
    if create_sample_go_program():
        # Test go run
        run_command(["go", "run", "hello.go"], "Running Go program")
        
        # Test go build
        run_command(["go", "build", "hello.go"], "Building Go program")
        
        # Test running the built executable
        if os.path.exists("hello.exe"):
            run_command(["hello.exe"], "Running built executable")
        elif os.path.exists("hello"):
            run_command(["./hello"], "Running built executable")

def simulate_server_commands():
    """Simulate the commands you would run on server"""
    print("Simulating Server Commands (Local Test)")
    print("=" * 50)
    
    # These are examples of commands you might run on the server
    server_commands = [
        ("go version", "Check Go version on server"),
        ("go env GOPATH", "Check Go workspace"),
        ("go mod init myproject", "Initialize Go module"),
        ("go get -u github.com/gin-gonic/gin", "Get a Go package"),
        ("go list -m all", "List all modules"),
    ]
    
    for cmd, desc in server_commands:
        run_command(cmd, f"SERVER SIMULATION: {desc}")

def main():
    """Main function"""
    print("Local Go Command Testing")
    print("=" * 60)
    print(f"Time: {datetime.now()}")
    print("=" * 60)
    
    print("What would you like to test?")
    print("1. Test Go commands")
    print("2. Test system commands")
    print("3. Test Go program execution")
    print("4. Simulate server commands")
    print("5. Run custom command")
    print("6. All tests")
    
    choice = input("Enter choice (1-6): ").strip()
    
    if choice == "1":
        test_go_commands()
    elif choice == "2":
        test_system_commands()
    elif choice == "3":
        test_go_program()
    elif choice == "4":
        simulate_server_commands()
    elif choice == "5":
        custom_cmd = input("Enter command to run: ").strip()
        if custom_cmd:
            run_command(custom_cmd, "Custom command")
    elif choice == "6":
        test_system_commands()
        print("\n")
        if test_go_commands():
            test_go_program()
            simulate_server_commands()
    else:
        print("❌ Invalid choice")

if __name__ == "__main__":
    main()

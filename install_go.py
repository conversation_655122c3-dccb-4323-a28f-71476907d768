#!/usr/bin/env python3
"""
Go Installation Helper
"""

import subprocess
import urllib.request
import os
import sys

def check_go_installed():
    """Check if Go is already installed"""
    try:
        result = subprocess.run(["go", "version"], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Go is already installed: {result.stdout.strip()}")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ Go is not installed")
    return False

def install_go_windows():
    """Install Go on Windows"""
    print("🔧 Installing Go on Windows...")
    
    # Go download URL for Windows
    go_url = "https://go.dev/dl/go1.21.5.windows-amd64.msi"
    
    print(f"📥 Downloading Go from: {go_url}")
    print("This will open your browser to download Go installer...")
    
    # Open browser to download page
    try:
        import webbrowser
        webbrowser.open("https://go.dev/dl/")
        print("✅ Browser opened to Go download page")
        print("\nManual installation steps:")
        print("1. Download the Windows installer (.msi file)")
        print("2. Run the installer as Administrator")
        print("3. Follow the installation wizard")
        print("4. Restart your terminal/PowerShell")
        print("5. Run 'go version' to verify installation")
        
    except Exception as e:
        print(f"💥 Error opening browser: {e}")
        print(f"Please manually visit: https://go.dev/dl/")

def install_go_chocolatey():
    """Install Go using Chocolatey (if available)"""
    print("🍫 Trying to install Go using Chocolatey...")
    
    try:
        # Check if chocolatey is available
        result = subprocess.run(["choco", "--version"], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Chocolatey found, installing Go...")
            
            install_result = subprocess.run(
                ["choco", "install", "golang", "-y"],
                capture_output=True,
                text=True
            )
            
            if install_result.returncode == 0:
                print("✅ Go installed successfully via Chocolatey!")
                print("Please restart your terminal and run 'go version'")
                return True
            else:
                print("❌ Chocolatey installation failed")
                print(install_result.stderr)
                return False
        else:
            print("❌ Chocolatey not found")
            return False
            
    except FileNotFoundError:
        print("❌ Chocolatey not found")
        return False

def main():
    """Main function"""
    print("Go Installation Helper")
    print("=" * 30)
    
    if check_go_installed():
        print("Go is already available! ✅")
        return
    
    print("\nInstallation options:")
    print("1. Manual installation (recommended)")
    print("2. Try Chocolatey installation")
    print("3. Show installation instructions")
    
    choice = input("Select option (1-3): ").strip()
    
    if choice == "1":
        install_go_windows()
    elif choice == "2":
        if not install_go_chocolatey():
            print("\nFalling back to manual installation...")
            install_go_windows()
    elif choice == "3":
        print("\n📋 Manual Installation Instructions:")
        print("1. Visit: https://go.dev/dl/")
        print("2. Download: go1.21.5.windows-amd64.msi")
        print("3. Run installer as Administrator")
        print("4. Add Go to PATH (installer should do this)")
        print("5. Restart terminal")
        print("6. Test with: go version")
        
        print("\n🔧 Alternative - Using PowerShell:")
        print("winget install GoLang.Go")
    else:
        print("❌ Invalid choice")

if __name__ == "__main__":
    main()

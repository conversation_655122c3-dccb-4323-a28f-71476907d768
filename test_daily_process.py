#!/usr/bin/env python3
"""
Test script for daily_process.py
Creates test files and directories to verify the automation works
"""

import os
import tempfile
import shutil
from datetime import datetime

def create_test_environment():
    """Create test directories and files"""
    
    # Create temporary directories
    test_base = os.path.join(os.getcwd(), "test_env")
    upload_dir = os.path.join(test_base, "upload")
    run_dir = os.path.join(test_base, "run")
    
    # Clean up if exists
    if os.path.exists(test_base):
        shutil.rmtree(test_base)
    
    # Create directories
    os.makedirs(upload_dir, exist_ok=True)
    os.makedirs(run_dir, exist_ok=True)
    
    print(f"✅ Created test directories:")
    print(f"   Upload: {upload_dir}")
    print(f"   Run: {run_dir}")
    
    # Create test files
    test_files = [
        "fo_secban_23082024.csv",
        "BSERISK20240823-00.spn", 
        "Security.txt",
        "Contract.txt",
        "Spd_contract.txt",
        "SCRIP_150623.TXT",
        "REG_IND22-0824.csv"
    ]
    
    for filename in test_files:
        file_path = os.path.join(upload_dir, filename)
        with open(file_path, 'w') as f:
            f.write(f"Test content for {filename}\n")
            f.write(f"Created: {datetime.now()}\n")
        print(f"   📄 Created: {filename}")
    
    # Create mock executables in run directory
    mock_executables = [
        "NSMload",
        "BulkDNSMload", 
        "SCMload",
        "Scrip_Master",
        "NseRegIndicator",
        "BseRegIndicator"
    ]
    
    for exe in mock_executables:
        if os.name == 'nt':  # Windows
            exe_path = os.path.join(run_dir, f"{exe}.bat")
            with open(exe_path, 'w') as f:
                f.write(f"@echo off\n")
                f.write(f"echo Mock execution of {exe} with args: %*\n")
                f.write(f"echo Completed successfully\n")
        else:  # Linux/Unix
            exe_path = os.path.join(run_dir, exe)
            with open(exe_path, 'w') as f:
                f.write(f"#!/bin/bash\n")
                f.write(f"echo 'Mock execution of {exe} with args: $@'\n")
                f.write(f"echo 'Completed successfully'\n")
            os.chmod(exe_path, 0o755)
        
        print(f"   🔧 Created mock: {exe}")
    
    return upload_dir, run_dir

def update_daily_process_config(upload_dir, run_dir):
    """Update daily_process.py to use test directories"""
    
    # Read the current daily_process.py
    with open('daily_process.py', 'r') as f:
        content = f.read()
    
    # Replace the directory paths
    content = content.replace(
        'UPLOAD_DIR = "/app/tcs/Application/Exec/ExcgFtp"',
        f'UPLOAD_DIR = r"{upload_dir}"'
    )
    content = content.replace(
        'RUN_DIR = "/app/tcs/Application/Exec/Run"',
        f'RUN_DIR = r"{run_dir}"'
    )
    
    # Write back
    with open('daily_process_test.py', 'w') as f:
        f.write(content)
    
    print(f"✅ Created daily_process_test.py with test configuration")

def main():
    """Main test function"""
    print("Daily Process Test Setup")
    print("=" * 40)
    
    # Create test environment
    upload_dir, run_dir = create_test_environment()
    
    # Update configuration
    update_daily_process_config(upload_dir, run_dir)
    
    print("\n" + "=" * 40)
    print("Test environment ready!")
    print("\nTo test:")
    print("1. Dry run:  python daily_process_test.py --dry")
    print("2. Real run: python daily_process_test.py")
    print("\nTest files created in:")
    print(f"  {upload_dir}")
    print(f"  {run_dir}")

if __name__ == "__main__":
    main()

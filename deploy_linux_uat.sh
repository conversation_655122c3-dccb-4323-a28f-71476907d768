#!/bin/bash
# Deploy daily_process.py to Linux UAT server
# This script prioritizes Linux UAT execution

UAT_SERVER="************"
UAT_USER="uatuser"
UAT_PATH="/app/tcs/Application/Exec"
SCRIPT_NAME="daily_process.py"

echo "=========================================="
echo "Linux UAT Deployment (Priority Environment)"
echo "=========================================="
echo "Server: $UAT_USER@$UAT_SERVER"
echo "Path: $UAT_PATH"
echo "=========================================="

# Check if script exists
if [ ! -f "$SCRIPT_NAME" ]; then
    echo "❌ $SCRIPT_NAME not found in current directory"
    exit 1
fi

# Test connectivity
echo "🔍 Testing connectivity to UAT server..."
if ping -c 1 $UAT_SERVER > /dev/null 2>&1; then
    echo "✅ UAT server is reachable"
else
    echo "❌ Cannot reach UAT server $UAT_SERVER"
    echo "Please check network connectivity or VPN"
    exit 1
fi

# Copy script to UAT
echo "📤 Copying $SCRIPT_NAME to UAT server..."
if scp $SCRIPT_NAME $UAT_USER@$UAT_SERVER:$UAT_PATH/; then
    echo "✅ File copied successfully"
else
    echo "❌ Failed to copy file"
    exit 1
fi

# Make executable
echo "🔧 Making script executable..."
ssh $UAT_USER@$UAT_SERVER "chmod +x $UAT_PATH/$SCRIPT_NAME"

# Test dry run
echo "🧪 Testing dry run on Linux UAT..."
ssh $UAT_USER@$UAT_SERVER "cd $UAT_PATH && python3 $SCRIPT_NAME --dry"

if [ $? -eq 0 ]; then
    echo "✅ Dry run successful on Linux UAT"
else
    echo "⚠️  Dry run had issues - check UAT environment"
fi

# Setup cron job
echo ""
echo "📅 To schedule daily execution, run on UAT server:"
echo "crontab -e"
echo "# Add this line for 7:00 AM daily:"
echo "0 7 * * * cd $UAT_PATH && python3 $SCRIPT_NAME >> $UAT_PATH/daily_process.log 2>&1"

echo ""
echo "🎉 Linux UAT deployment completed!"
echo "The script will run on Linux UAT under any condition."
echo "Windows is available as fallback if needed."

# BSE SCRIP Downloader

Python automation script to download Scrip Master file (SCRIP.ZIP) for Equity segment from BSE India.

## Features

- **SOD (Start of Day)**: Download fresh SCRIP data
- **EOD (End of Day)**: Download and archive SCRIP data
- **Manual Download**: On-demand download
- **Auto-extraction**: Automatically extracts ZIP files
- **Logging**: Comprehensive logging of all operations
- **Error Handling**: Robust error handling with multiple URL fallbacks

## Setup

1. **Install Python** (3.6 or higher)

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

### Method 1: Interactive Mode
```bash
python bse_scrip_downloader.py
```
Then select:
- `1` for SOD (Start of Day)
- `2` for EOD (End of Day) 
- `3` for Manual download

### Method 2: Batch File (Windows)
Double-click `run_sod.bat` for quick SOD execution

### Method 3: Direct Python Import
```python
from bse_scrip_downloader import BSEScripDownloader

downloader = BSEScripDownloader()

# SOD routine
result = downloader.sod_routine()

# EOD routine  
result = downloader.eod_routine()

# Manual download
result = downloader.download_latest_scrip(extract=True)
```

## File Structure

```
python-automation/
├── bse_scrip_downloader.py    # Main script
├── config.py                  # Configuration settings
├── requirements.txt           # Python dependencies
├── run_sod.bat               # Windows batch file
├── README.md                 # This file
├── downloads/                # Downloaded files
│   ├── SCRIP_YYYYMMDD_HHMMSS.zip
│   ├── extracted/            # Extracted files
│   └── archive/              # Archived files by date
└── bse_download.log          # Log file
```

## Configuration

Edit `config.py` to customize:
- Download directories
- BSE URLs
- Timing settings
- Logging preferences

## Logging

All operations are logged to:
- Console output
- `bse_download.log` file

## Error Handling

The script includes:
- Multiple URL fallbacks
- Network timeout handling
- File validation
- Comprehensive error logging

## Scheduling

To automate SOD/EOD routines, you can:

### Windows Task Scheduler
1. Open Task Scheduler
2. Create Basic Task
3. Set trigger (daily at SOD/EOD time)
4. Set action to run `python bse_scrip_downloader.py`

### Linux Cron
```bash
# SOD at 9:00 AM
0 9 * * * cd /path/to/script && python bse_scrip_downloader.py

# EOD at 6:00 PM  
0 18 * * * cd /path/to/script && python bse_scrip_downloader.py
```

## Troubleshooting

1. **Download fails**: Check internet connection and BSE website availability
2. **Permission errors**: Run as administrator or check folder permissions
3. **ZIP extraction fails**: Verify downloaded file is valid ZIP format
4. **Import errors**: Ensure all dependencies are installed

## Next Steps

This script provides the foundation for:
- Exchange data automation
- Market data processing
- Trading system integration
- Data analysis pipelines

## Support

Check the log files for detailed error information. The script tries multiple URLs automatically if one fails.

# Daily Process Automation

Automated file processing and command execution for daily exchange operations.

## Overview

This script automates the daily process of:
1. **File Renaming**: Renames incoming files according to predefined templates
2. **Command Execution**: Runs a sequence of processing commands

## Usage

### Basic Usage
```bash
# Run the full process (rename files + execute commands)
python daily_process.py

# Dry run (show what would happen without doing it)
python daily_process.py --dry
```

### Configuration

Edit the configuration section in `daily_process.py`:

```python
# Directories
UPLOAD_DIR = "/app/tcs/Application/Exec/ExcgFtp"   # Where files are dropped
RUN_DIR = "/app/tcs/Application/Exec/Run"          # Where commands are executed

# File rename templates
RENAME_TEMPLATES = {
    "fo_secban_*.csv": "fo_secban_{DDMONYYYY}.csv",
    "BSERISK*-00.spn": "Nsocl.{YYYYMMDD}.i01.spn",
    "Security.txt": "NSM{YYYYMMDD}.{BATCH}.DAT",
    # ... add more patterns as needed
}

# Commands to execute
COMMANDS = [
    f"cd {RUN_DIR} && ./NSMload {YYYYMMDD}",
    f"cd {RUN_DIR} && ./BulkDNSMload {YYYYMMDD}",
    # ... add more commands as needed
]
```

## File Rename Examples

| Input File | Output File | Template Used |
|------------|-------------|---------------|
| `fo_secban_23082024.csv` | `fo_secban_23-Aug-2024.csv` | `fo_secban_{DDMONYYYY}.csv` |
| `BSERISK20240823-00.spn` | `Nsocl.20240823.i01.spn` | `Nsocl.{YYYYMMDD}.i01.spn` |
| `Security.txt` | `NSM20240823.23.DAT` | `NSM{YYYYMMDD}.{BATCH}.DAT` |
| `Contract.txt` | `DNSM20240823.23.DAT` | `DNSM{YYYYMMDD}.{BATCH}.DAT` |

## Commands Executed

The script runs these commands in sequence:
1. `NSMload` - Load NSE master data
2. `BulkDNSMload` - Load derivative NSE master data  
3. `SCMload` - Load spread contract master data
4. `Scrip_Master` - Load scrip master data
5. `NseRegIndicator` - Process NSE regulatory indicators
6. `BseRegIndicator` - Process BSE regulatory indicators

## Date Placeholders

- `{YYYYMMDD}` - Date in YYYYMMDD format (e.g., 20240823)
- `{DDMONYYYY}` - Date in DD-Mon-YYYY format (e.g., 23-Aug-2024)
- `{BATCH}` - Batch number (default: "23")

## Testing

### Local Testing (Windows)
```bash
# Test with dry run first
python daily_process.py --dry

# Create test directories and files
mkdir test_upload
mkdir test_run
echo "test" > test_upload/Security.txt
```

### Production (Linux Server)
```bash
# Deploy to server
scp daily_process.py user@server:/app/tcs/Application/Exec/

# Run on server
ssh user@server "cd /app/tcs/Application/Exec && python3 daily_process.py --dry"
```

## Features

- ✅ **Dry Run Mode**: Test without making changes
- ✅ **Backup**: Automatically backs up existing files before overwriting
- ✅ **Logging**: Shows detailed output of all operations
- ✅ **Error Handling**: Continues processing even if individual commands fail
- ✅ **Configurable**: Easy to modify file patterns and commands

## Troubleshooting

### Common Issues

1. **Files not found**: Check `UPLOAD_DIR` path is correct
2. **Commands fail**: Verify `RUN_DIR` path and executable permissions
3. **Permission errors**: Ensure script has read/write access to directories

### Debug Mode
```bash
# Run with verbose output
python daily_process.py --dry  # Shows what would happen
```

## Scheduling

### Linux Cron
```bash
# Run daily at 6:00 AM
0 6 * * * cd /app/tcs/Application/Exec && python3 daily_process.py >> daily_process.log 2>&1
```

### Windows Task Scheduler
1. Create new task
2. Set trigger: Daily at 6:00 AM
3. Set action: Start program `python` with arguments `daily_process.py`
4. Set start in: `C:\path\to\script\directory`

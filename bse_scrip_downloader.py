#!/usr/bin/env python3
"""
BSE SCRIP.ZIP Downloader
Automation script to download Scrip Master file for Equity segment from BSE India
"""

import urllib.request
import urllib.error
import os
import zipfile
from datetime import datetime
import logging
import time
import ssl
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bse_download.log'),
        logging.StreamHandler()
    ]
)

class BSEScripDownloader:
    def __init__(self, download_dir="downloads"):
        """
        Initialize BSE Scrip Downloader
        
        Args:
            download_dir (str): Directory to save downloaded files
        """
        self.download_dir = Path(download_dir)
        self.download_dir.mkdir(exist_ok=True)
        
        # BSE URLs - these are common patterns for BSE downloads
        self.base_urls = [
            "https://www.bseindia.com/download/BhavCopy/Equity/SCRIP.ZIP",
            "https://www.bseindia.com/downloads/SCRIP.ZIP",
            "https://www.bseindia.com/corporates/SCRIP.ZIP",
            "https://www.bseindia.com/static/markets/equity/EQT_L.zip"
        ]
        
        # Headers to mimic browser request
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }

        # Create SSL context that doesn't verify certificates (for some BSE URLs)
        self.ssl_context = ssl.create_default_context()
        self.ssl_context.check_hostname = False
        self.ssl_context.verify_mode = ssl.CERT_NONE
    
    def download_scrip_file(self, url, filename=None):
        """
        Download SCRIP file from given URL

        Args:
            url (str): URL to download from
            filename (str): Custom filename (optional)

        Returns:
            str: Path to downloaded file or None if failed
        """
        try:
            logging.info(f"Attempting to download from: {url}")

            # Create request with headers
            request = urllib.request.Request(url, headers=self.headers)

            # Open URL with SSL context
            with urllib.request.urlopen(request, context=self.ssl_context, timeout=30) as response:
                # Generate filename if not provided
                if not filename:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    filename = f"SCRIP_{timestamp}.zip"

                file_path = self.download_dir / filename

                # Save the file
                with open(file_path, 'wb') as f:
                    f.write(response.read())

                file_size = os.path.getsize(file_path)
                logging.info(f"Successfully downloaded: {filename} ({file_size} bytes)")

                return str(file_path)

        except urllib.error.HTTPError as e:
            logging.error(f"HTTP Error downloading from {url}: {e.code} - {e.reason}")
            return None
        except urllib.error.URLError as e:
            logging.error(f"URL Error downloading from {url}: {e.reason}")
            return None
        except Exception as e:
            logging.error(f"Unexpected error downloading from {url}: {e}")
            return None
    
    def extract_zip_file(self, zip_path, extract_dir=None):
        """
        Extract downloaded ZIP file
        
        Args:
            zip_path (str): Path to ZIP file
            extract_dir (str): Directory to extract to (optional)
            
        Returns:
            str: Path to extraction directory or None if failed
        """
        try:
            if not extract_dir:
                extract_dir = self.download_dir / "extracted"
            
            extract_dir = Path(extract_dir)
            extract_dir.mkdir(exist_ok=True)
            
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(extract_dir)
            
            logging.info(f"Successfully extracted {zip_path} to {extract_dir}")
            
            # List extracted files
            extracted_files = list(extract_dir.glob("*"))
            logging.info(f"Extracted files: {[f.name for f in extracted_files]}")
            
            return str(extract_dir)
            
        except zipfile.BadZipFile:
            logging.error(f"Invalid ZIP file: {zip_path}")
            return None
        except Exception as e:
            logging.error(f"Error extracting {zip_path}: {e}")
            return None
    
    def download_latest_scrip(self, extract=True):
        """
        Download the latest SCRIP file by trying multiple URLs
        
        Args:
            extract (bool): Whether to extract the ZIP file
            
        Returns:
            dict: Download result with file paths
        """
        result = {
            'success': False,
            'zip_path': None,
            'extract_path': None,
            'url_used': None
        }
        
        for url in self.base_urls:
            zip_path = self.download_scrip_file(url)
            
            if zip_path:
                result['success'] = True
                result['zip_path'] = zip_path
                result['url_used'] = url
                
                if extract:
                    extract_path = self.extract_zip_file(zip_path)
                    result['extract_path'] = extract_path
                
                break
            
            # Wait between attempts
            time.sleep(1)
        
        if not result['success']:
            logging.error("Failed to download SCRIP file from any URL")
        
        return result
    
    def sod_routine(self):
        """Start of Day routine - download fresh SCRIP data"""
        logging.info("=== SOD (Start of Day) Routine ===")
        return self.download_latest_scrip(extract=True)
    
    def eod_routine(self):
        """End of Day routine - download and archive SCRIP data"""
        logging.info("=== EOD (End of Day) Routine ===")
        result = self.download_latest_scrip(extract=True)
        
        if result['success']:
            # Archive with timestamp
            timestamp = datetime.now().strftime("%Y%m%d")
            archive_dir = self.download_dir / "archive" / timestamp
            archive_dir.mkdir(parents=True, exist_ok=True)
            
            # Copy files to archive
            if result['zip_path']:
                import shutil
                archive_zip = archive_dir / f"SCRIP_{timestamp}.zip"
                shutil.copy2(result['zip_path'], archive_zip)
                logging.info(f"Archived to: {archive_zip}")
        
        return result

def main():
    """Main function to run the downloader"""
    downloader = BSEScripDownloader()
    
    print("BSE SCRIP Downloader")
    print("1. SOD (Start of Day) - Download fresh data")
    print("2. EOD (End of Day) - Download and archive")
    print("3. Manual download")
    
    choice = input("Enter your choice (1-3): ").strip()
    
    if choice == "1":
        result = downloader.sod_routine()
    elif choice == "2":
        result = downloader.eod_routine()
    elif choice == "3":
        result = downloader.download_latest_scrip(extract=True)
    else:
        print("Invalid choice")
        return
    
    if result['success']:
        print(f"\n✅ Download successful!")
        print(f"ZIP file: {result['zip_path']}")
        if result['extract_path']:
            print(f"Extracted to: {result['extract_path']}")
        print(f"URL used: {result['url_used']}")
    else:
        print("\n❌ Download failed. Check logs for details.")

if __name__ == "__main__":
    main()

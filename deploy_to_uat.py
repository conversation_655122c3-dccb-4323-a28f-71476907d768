#!/usr/bin/env python3
"""
Deployment script for UAT Linux server
"""

import subprocess
import os
import sys

# UAT Server Configuration
UAT_SERVER = "************"  # Replace with actual UAT server IP
UAT_USER = "uatuser"         # Replace with actual UAT username
UAT_PATH = "/app/tcs/Application/Exec"
SCRIPT_NAME = "daily_process.py"

def deploy_to_uat():
    """Deploy daily_process.py to UAT server"""
    
    print("Deploying to UAT Linux Server")
    print("=" * 40)
    print(f"Server: {UAT_USER}@{UAT_SERVER}")
    print(f"Path: {UAT_PATH}")
    print("=" * 40)
    
    if not os.path.exists(SCRIPT_NAME):
        print(f"❌ {SCRIPT_NAME} not found in current directory")
        return False
    
    try:
        # Copy file to UAT server
        scp_cmd = f"scp {SCRIPT_NAME} {UAT_USER}@{UAT_SERVER}:{UAT_PATH}/"
        print(f"📤 Copying file: {scp_cmd}")
        
        result = subprocess.run(scp_cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"❌ SCP failed: {result.stderr}")
            return False
        
        print("✅ File copied successfully")
        
        # Make executable
        chmod_cmd = f"ssh {UAT_USER}@{UAT_SERVER} 'chmod +x {UAT_PATH}/{SCRIPT_NAME}'"
        print(f"🔧 Making executable: chmod +x")
        
        result = subprocess.run(chmod_cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"⚠️  chmod warning: {result.stderr}")
        else:
            print("✅ File made executable")
        
        # Test dry run on UAT
        test_cmd = f"ssh {UAT_USER}@{UAT_SERVER} 'cd {UAT_PATH} && python3 {SCRIPT_NAME} --dry'"
        print(f"🧪 Testing dry run on UAT...")
        
        result = subprocess.run(test_cmd, shell=True, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Dry run test successful")
            print("UAT Output:")
            print("-" * 30)
            print(result.stdout)
            print("-" * 30)
        else:
            print(f"⚠️  Dry run test failed: {result.stderr}")
        
        return True
        
    except Exception as e:
        print(f"💥 Deployment error: {e}")
        return False

def create_uat_cron():
    """Create cron job on UAT server"""
    
    cron_entry = f"0 7 * * * cd {UAT_PATH} && python3 {SCRIPT_NAME} >> {UAT_PATH}/daily_process.log 2>&1"
    
    print("\n📅 Cron Job Setup")
    print("=" * 30)
    print("To schedule daily execution at 7:00 AM, run this on UAT server:")
    print()
    print(f"ssh {UAT_USER}@{UAT_SERVER}")
    print("crontab -e")
    print("# Add this line:")
    print(cron_entry)
    print()
    print("Or run this command:")
    print(f"ssh {UAT_USER}@{UAT_SERVER} '(crontab -l 2>/dev/null; echo \"{cron_entry}\") | crontab -'")

def main():
    """Main deployment function"""
    
    if len(sys.argv) > 1 and sys.argv[1] == "--cron":
        create_uat_cron()
        return
    
    print("Daily Process UAT Deployment")
    print("=" * 50)
    
    # Check if we can reach UAT server
    ping_cmd = f"ping -n 1 {UAT_SERVER}" if os.name == 'nt' else f"ping -c 1 {UAT_SERVER}"
    
    print(f"🔍 Checking connectivity to {UAT_SERVER}...")
    result = subprocess.run(ping_cmd, shell=True, capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"❌ Cannot reach UAT server {UAT_SERVER}")
        print("Please check:")
        print("1. Server IP address is correct")
        print("2. Network connectivity")
        print("3. VPN connection if required")
        return
    
    print("✅ UAT server is reachable")
    
    # Deploy
    if deploy_to_uat():
        print("\n🎉 Deployment successful!")
        print("\nNext steps:")
        print("1. Test manually on UAT:")
        print(f"   ssh {UAT_USER}@{UAT_SERVER}")
        print(f"   cd {UAT_PATH}")
        print(f"   python3 {SCRIPT_NAME} --dry")
        print()
        print("2. Schedule cron job:")
        print("   python deploy_to_uat.py --cron")
    else:
        print("\n❌ Deployment failed!")

if __name__ == "__main__":
    main()

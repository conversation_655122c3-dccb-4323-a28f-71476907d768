#!/usr/bin/env python3
"""
Remote Command Execution Script
For executing commands on remote systems
"""

import subprocess
import socket
import sys
from datetime import datetime

class RemoteCommandExecutor:
    def __init__(self, target_ip):
        self.target_ip = target_ip
    
    def check_connectivity(self, port=22):
        """Check if target is reachable"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            result = sock.connect_ex((self.target_ip, port))
            sock.close()
            return result == 0
        except:
            return False
    
    def ping_test(self):
        """Quick ping test"""
        try:
            result = subprocess.run(
                ["ping", "-n", "1", self.target_ip], 
                capture_output=True, 
                text=True, 
                timeout=5
            )
            return result.returncode == 0
        except:
            return False
    
    def execute_ssh_command(self, command, username="admin", port=22):
        """Execute command via SSH (requires SSH client)"""
        if not self.check_connectivity(port):
            print(f"❌ Cannot connect to {self.target_ip}:{port}")
            return False
        
        try:
            ssh_cmd = [
                "ssh", 
                "-o", "ConnectTimeout=10",
                "-o", "StrictHostKeyChecking=no",
                f"{username}@{self.target_ip}",
                command
            ]
            
            print(f"🔧 Executing: {command}")
            print(f"📡 Target: {username}@{self.target_ip}")
            
            result = subprocess.run(ssh_cmd, timeout=30)
            return result.returncode == 0
            
        except subprocess.TimeoutExpired:
            print("⏰ SSH command timed out")
            return False
        except FileNotFoundError:
            print("❌ SSH client not found. Install OpenSSH or PuTTY")
            return False
        except Exception as e:
            print(f"💥 SSH error: {e}")
            return False
    
    def execute_powershell_remote(self, command, username="Administrator"):
        """Execute PowerShell command remotely (Windows)"""
        try:
            ps_cmd = [
                "powershell", "-Command",
                f"Invoke-Command -ComputerName {self.target_ip} -ScriptBlock {{{command}}} -Credential (Get-Credential {username})"
            ]
            
            print(f"⚡ Executing PowerShell: {command}")
            print(f"📡 Target: {self.target_ip}")
            
            result = subprocess.run(ps_cmd, timeout=30)
            return result.returncode == 0
            
        except Exception as e:
            print(f"💥 PowerShell error: {e}")
            return False
    
    def execute_wmi_command(self, command):
        """Execute command via WMI (Windows Management Instrumentation)"""
        try:
            wmi_cmd = [
                "wmic", "/node:" + self.target_ip,
                "process", "call", "create", f'"{command}"'
            ]
            
            print(f"🔧 Executing via WMI: {command}")
            print(f"📡 Target: {self.target_ip}")
            
            result = subprocess.run(wmi_cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                print("✅ WMI command executed")
                print(result.stdout)
                return True
            else:
                print("❌ WMI command failed")
                print(result.stderr)
                return False
                
        except Exception as e:
            print(f"💥 WMI error: {e}")
            return False

def main():
    """Main function"""
    target_ip = "************"
    
    print("Remote Command Executor")
    print("=" * 40)
    print(f"Target: {target_ip}")
    print(f"Time: {datetime.now()}")
    print("=" * 40)
    
    executor = RemoteCommandExecutor(target_ip)
    
    # Check connectivity first
    print("🔍 Checking connectivity...")
    
    if executor.ping_test():
        print("✅ Ping successful")
    else:
        print("❌ Ping failed")
    
    if executor.check_connectivity(22):
        print("✅ SSH port (22) is open")
    else:
        print("❌ SSH port (22) is closed/filtered")
    
    if executor.check_connectivity(3389):
        print("✅ RDP port (3389) is open")
    else:
        print("❌ RDP port (3389) is closed/filtered")
    
    print("\n" + "=" * 40)
    print("Available command methods:")
    print("1. SSH (Linux/Unix systems)")
    print("2. PowerShell Remoting (Windows)")
    print("3. WMI (Windows)")
    print("4. Manual command builder")
    print("=" * 40)
    
    choice = input("Select method (1-4) or 'q' to quit: ").strip()
    
    if choice == "1":
        username = input("SSH Username [admin]: ").strip() or "admin"
        command = input("Command to execute: ").strip()
        if command:
            executor.execute_ssh_command(command, username)
    
    elif choice == "2":
        username = input("Windows Username [Administrator]: ").strip() or "Administrator"
        command = input("PowerShell command to execute: ").strip()
        if command:
            executor.execute_powershell_remote(command, username)
    
    elif choice == "3":
        command = input("Command to execute via WMI: ").strip()
        if command:
            executor.execute_wmi_command(command)
    
    elif choice == "4":
        print("\nManual command examples:")
        print(f"SSH: ssh admin@{target_ip} 'your_command_here'")
        print(f"SCP: scp file.txt admin@{target_ip}:/path/to/destination/")
        print(f"Telnet: telnet {target_ip} 23")
        print(f"RDP: mstsc /v:{target_ip}")
        
    elif choice.lower() == "q":
        print("👋 Goodbye!")
    
    else:
        print("❌ Invalid choice")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Simple test script to download BSE SCRIP file
"""

import urllib.request
import urllib.error
import os
import ssl
from datetime import datetime

def test_download():
    """Test download from BSE"""
    
    # Create downloads directory
    if not os.path.exists('downloads'):
        os.makedirs('downloads')
    
    # BSE URLs to try
    urls = [
        "https://www.bseindia.com/download/BhavCopy/Equity/SCRIP.ZIP",
        "https://www.bseindia.com/downloads/SCRIP.ZIP",
        "https://www.bseindia.com/corporates/SCRIP.ZIP",
        "https://www.bseindia.com/static/markets/equity/EQT_L.zip"
    ]
    
    # Headers
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    # SSL context
    ssl_context = ssl.create_default_context()
    ssl_context.check_hostname = False
    ssl_context.verify_mode = ssl.CERT_NONE
    
    for i, url in enumerate(urls, 1):
        print(f"\n{i}. Trying: {url}")
        
        try:
            request = urllib.request.Request(url, headers=headers)
            
            with urllib.request.urlopen(request, context=ssl_context, timeout=30) as response:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"downloads/SCRIP_{timestamp}_test{i}.zip"
                
                with open(filename, 'wb') as f:
                    data = response.read()
                    f.write(data)
                
                file_size = len(data)
                print(f"   ✅ Success! Downloaded {file_size} bytes to {filename}")
                
                # Check if it's a valid file
                if file_size > 100:  # Reasonable minimum size
                    print(f"   📁 File seems valid (size: {file_size} bytes)")
                    return filename
                else:
                    print(f"   ⚠️  File too small, might be an error page")
                    
        except urllib.error.HTTPError as e:
            print(f"   ❌ HTTP Error: {e.code} - {e.reason}")
        except urllib.error.URLError as e:
            print(f"   ❌ URL Error: {e.reason}")
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print("\n❌ All download attempts failed")
    return None

if __name__ == "__main__":
    print("BSE SCRIP Download Test")
    print("=" * 30)
    
    result = test_download()
    
    if result:
        print(f"\n🎉 Download successful: {result}")
        
        # List files in downloads directory
        print("\nFiles in downloads directory:")
        for file in os.listdir('downloads'):
            size = os.path.getsize(f'downloads/{file}')
            print(f"  - {file} ({size} bytes)")
    else:
        print("\n💥 Download failed")
        print("\nPossible reasons:")
        print("1. BSE website is down or URLs have changed")
        print("2. Network connectivity issues")
        print("3. BSE requires authentication or has anti-bot measures")
        print("4. Files are only available during market hours")

#!/usr/bin/env python3
"""
Simple Go Command Test
"""

import subprocess

def test_go():
    """Test Go command"""
    print("Testing Go command...")
    
    try:
        # Test go version
        result = subprocess.run(
            ["go", "version"], 
            capture_output=True, 
            text=True, 
            timeout=10
        )
        
        if result.returncode == 0:
            print("✅ Go is available!")
            print(f"Version: {result.stdout.strip()}")
            
            # Test go help
            help_result = subprocess.run(
                ["go", "help"], 
                capture_output=True, 
                text=True, 
                timeout=10
            )
            
            if help_result.returncode == 0:
                print("\n📋 Go help (first 10 lines):")
                lines = help_result.stdout.split('\n')[:10]
                for line in lines:
                    if line.strip():
                        print(f"  {line}")
            
            return True
            
        else:
            print("❌ Go command failed")
            print(f"Error: {result.stderr}")
            return False
            
    except FileNotFoundError:
        print("❌ Go is not installed")
        print("Install Go from: https://go.dev/dl/")
        return False
    except Exception as e:
        print(f"💥 Error: {e}")
        return False

def test_alternative_commands():
    """Test alternative commands that might work"""
    print("\nTesting alternative commands...")
    
    commands = [
        "echo Hello World",
        "python --version", 
        "dir /b",
        "whoami"
    ]
    
    for cmd in commands:
        try:
            result = subprocess.run(
                cmd, 
                shell=True,
                capture_output=True, 
                text=True, 
                timeout=5
            )
            
            if result.returncode == 0:
                print(f"✅ {cmd}: {result.stdout.strip()}")
            else:
                print(f"❌ {cmd}: Failed")
                
        except Exception as e:
            print(f"💥 {cmd}: Error - {e}")

if __name__ == "__main__":
    print("Simple Go Command Test")
    print("=" * 30)
    
    go_available = test_go()
    
    if not go_available:
        print("\n🔧 Go is not available. Testing other commands...")
        test_alternative_commands()
        
        print("\n💡 To install Go:")
        print("1. Download from: https://go.dev/dl/")
        print("2. Or use: winget install GoLang.Go")
        print("3. Or use: choco install golang")
    
    print("\n✅ Test completed!")

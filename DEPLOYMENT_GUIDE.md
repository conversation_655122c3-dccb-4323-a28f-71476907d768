# Daily Process Automation - Deployment Guide

## Priority: Linux UAT First, Windows Fallback

This automation is designed to **work on Linux UAT under any condition**, with Windows as a fallback option.

## 🐧 Linux UAT (Primary Environment)

### Configuration
- **Upload Directory**: `/app/tcs/Application/Exec/ExcgFtp`
- **Run Directory**: `/app/tcs/Application/Exec/Run`
- **Executables**: `NSMload`, `BulkDNSMload`, `SCMload`, `Scrip_Master`, `NseRegIndicator`, `BseRegIndicator`

### Deployment to Linux UAT

1. **Copy script to UAT server**:
   ```bash
   scp daily_process.py uatuser@************:/app/tcs/Application/Exec/
   ```

2. **Make executable**:
   ```bash
   ssh uatuser@************ "chmod +x /app/tcs/Application/Exec/daily_process.py"
   ```

3. **Test on UAT**:
   ```bash
   ssh uatuser@************ "cd /app/tcs/Application/Exec && python3 daily_process.py --dry"
   ```

4. **Run for real**:
   ```bash
   ssh uatuser@************ "cd /app/tcs/Application/Exec && python3 daily_process.py"
   ```

### Automated Deployment
Use the deployment script:
```bash
chmod +x deploy_linux_uat.sh
./deploy_linux_uat.sh
```

### Schedule on Linux UAT
```bash
# Login to UAT server
ssh uatuser@************

# Add to crontab for daily 7:00 AM execution
crontab -e

# Add this line:
0 7 * * * cd /app/tcs/Application/Exec && python3 daily_process.py >> /app/tcs/Application/Exec/daily_process.log 2>&1
```

## 🪟 Windows (Fallback Environment)

### Configuration
- **Upload Directory**: `C:\Users\<USER>\Desktop\python-automation\windows_upload`
- **Run Directory**: `C:\Users\<USER>\Desktop\python-automation\windows_run`
- **Executables**: `NSMload.bat`, `BulkDNSMload.bat`, etc.

### Windows Testing
```bash
# Test on Windows (fallback)
python daily_process.py --dry
python daily_process.py --setup  # Setup test environment
python daily_process.py          # Run for real
```

## 📋 File Processing

### Files Renamed (Both Environments)
| Input File | Output File | Purpose |
|------------|-------------|---------|
| `fo_secban_23082024.csv` | `fo_secban_19-Sep-2025.csv` | NSE/BSE security ban file |
| `BSERISK20240823-00.spn` | `Nsocl.20250919.i01.spn` | BSE SPAN to NSE SPAN format |
| `Security.txt` | `NSM20250919.23.DAT` | Security master data |
| `Contract.txt` | `DNSM20250919.23.DAT` | Contract master data |
| `Spd_contract.txt` | `SCM20250919.23.DAT` | Spread contract data |
| `SCRIP_150623.TXT` | `BSM20250919.23.DAT` | SCRIP master data |
| `REG_IND22-0824.csv` | `REG_IND22-20250919_23.DAT` | Regulatory indicator |

### Commands Executed (Both Environments)
1. `NSMload 20250919` - Load NSE master data
2. `BulkDNSMload 20250919` - Load derivative NSE master data
3. `SCMload 20250919` - Load spread contract master data
4. `Scrip_Master 20250919` - Load scrip master data
5. `NseRegIndicator 20250919` - Process NSE regulatory indicators
6. `BseRegIndicator 20250919` - Process BSE regulatory indicators

## 🚀 Usage

### Linux UAT (Primary)
```bash
# Dry run (recommended first)
python3 daily_process.py --dry

# Real execution
python3 daily_process.py
```

### Windows (Fallback)
```bash
# Dry run (recommended first)
python daily_process.py --dry

# Setup test environment
python daily_process.py --setup

# Real execution
python daily_process.py
```

## 🔧 Environment Detection

The script automatically detects the environment:

- **Linux**: Uses production UAT paths and executables
- **Windows**: Uses fallback test paths and mock executables
- **Unknown**: Defaults to Linux UAT settings

## ✅ Success Criteria

### Linux UAT Success
- ✅ All files renamed according to templates
- ✅ All 6 commands executed successfully
- ✅ Logs written to `/app/tcs/Application/Exec/daily_process.log`

### Windows Fallback Success
- ✅ Mock environment created automatically
- ✅ File renaming tested with sample files
- ✅ Mock commands executed for testing

## 🆘 Troubleshooting

### Linux UAT Issues
1. **Permission denied**: Check file permissions and user access
2. **Directory not found**: Verify UAT paths exist
3. **Executable not found**: Check if executables are in PATH or run directory

### Windows Fallback Issues
1. **Directory creation failed**: Check Windows permissions
2. **Mock files not created**: Verify write access to test directories

## 📊 Monitoring

### Check Logs
```bash
# On Linux UAT
tail -f /app/tcs/Application/Exec/daily_process.log

# On Windows
# Check console output or redirect to file
python daily_process.py >> daily_process.log 2>&1
```

### Verify Execution
```bash
# Check if files were processed
ls -la /app/tcs/Application/Exec/ExcgFtp/

# Check if commands ran
ps aux | grep -E "(NSMload|BulkDNSMload|SCMload)"
```

## 🎯 Summary

**Priority**: Linux UAT works under any condition
**Fallback**: Windows available if Linux UAT fails
**Automation**: Fully automated file processing and command execution
**Monitoring**: Comprehensive logging and error handling
**Deployment**: Simple one-command deployment to UAT

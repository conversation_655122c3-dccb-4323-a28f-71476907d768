#!/usr/bin/env python3
"""
Go Command Executor
"""

import subprocess
import sys

def run_go_command(args=None):
    """Run go command with arguments"""
    
    if args is None:
        args = ["version"]  # Default to version check
    
    try:
        # Construct the go command
        cmd = ["go"] + args
        
        print(f"🚀 Running: {' '.join(cmd)}")
        print("-" * 40)
        
        # Execute the command
        result = subprocess.run(
            cmd,
            capture_output=True,
            text=True,
            timeout=30
        )
        
        # Print output
        if result.stdout:
            print("Output:")
            print(result.stdout)
        
        if result.stderr:
            print("Errors:")
            print(result.stderr)
        
        print(f"Return code: {result.returncode}")
        
        return result.returncode == 0
        
    except FileNotFoundError:
        print("❌ Go is not installed or not in PATH")
        print("Install Go from: https://golang.org/dl/")
        return False
    except subprocess.TimeoutExpired:
        print("⏰ Command timed out")
        return False
    except Exception as e:
        print(f"💥 Error: {e}")
        return False

def main():
    """Main function"""
    print("Go Command Executor")
    print("=" * 30)
    
    # Check if Go is available
    if run_go_command(["version"]):
        print("\n✅ Go is available!")
        
        print("\nCommon Go commands:")
        print("1. go version")
        print("2. go env")
        print("3. go mod init")
        print("4. go build")
        print("5. go run")
        print("6. Custom command")
        
        choice = input("\nSelect option (1-6): ").strip()
        
        if choice == "1":
            run_go_command(["version"])
        elif choice == "2":
            run_go_command(["env"])
        elif choice == "3":
            module_name = input("Module name: ").strip()
            if module_name:
                run_go_command(["mod", "init", module_name])
        elif choice == "4":
            run_go_command(["build"])
        elif choice == "5":
            file_name = input("Go file to run: ").strip()
            if file_name:
                run_go_command(["run", file_name])
        elif choice == "6":
            custom_args = input("Enter go command arguments: ").strip().split()
            if custom_args:
                run_go_command(custom_args)
    else:
        print("\n❌ Go is not available")

if __name__ == "__main__":
    main()

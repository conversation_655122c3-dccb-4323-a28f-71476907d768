#!/usr/bin/env python3
"""
daily_process.py

- Looks for expected files in UPLOAD_DIR (where files are dropped automatically).
- Renames files according to templates below (configurable).
- Runs the sequence of commands used by your daily process.

Usage:
    python3 daily_process.py        # runs, renames, executes commands
    python3 daily_process.py --dry  # shows actions without performing them
"""

import os
import shutil
import datetime
import glob
import subprocess
import argparse
import platform
from typing import Dict

# ===========================
# CONFIG - Edit these values
# ===========================

# Detect operating system
IS_WINDOWS = platform.system().lower() == "windows"
IS_LINUX = platform.system().lower() == "linux"

# Environment-specific configuration
if IS_WINDOWS:
    # Windows development environment (compulsory)
    UPLOAD_DIR = r"C:\temp\upload"                    # Windows test directory
    RUN_DIR = r"C:\temp\run"                          # Windows test directory
    EXECUTABLE_EXT = ".bat"                           # Windows batch files
    SHELL_PREFIX = "cmd /c"                           # Windows command prefix
    PATH_SEPARATOR = "\\"
    CD_COMMAND = "cd /d"                              # Windows change directory
else:
    # Linux UAT/Production environment (compulsory)
    UPLOAD_DIR = "/app/tcs/Application/Exec/ExcgFtp"  # Linux UAT directory
    RUN_DIR = "/app/tcs/Application/Exec/Run"         # Linux UAT directory
    EXECUTABLE_EXT = ""                               # Linux executables (no extension)
    SHELL_PREFIX = ""                                 # Linux direct execution
    PATH_SEPARATOR = "/"
    CD_COMMAND = "cd"                                 # Linux change directory

# Create directories if they don't exist (for Windows development)
if IS_WINDOWS:
    os.makedirs(UPLOAD_DIR, exist_ok=True)
    os.makedirs(RUN_DIR, exist_ok=True)

TODAY = datetime.datetime.now()
YYYYMMDD = TODAY.strftime("%Y%m%d")
DD_MON_YYYY = TODAY.strftime("%d-%b-%Y")           # e.g. 23-Aug-2024
BATCH = "23"   # document examples show a ".23" or "_23" part in DAT names; change if needed

print(f"Running on: {platform.system()}")
print(f"Upload Directory: {UPLOAD_DIR}")
print(f"Run Directory: {RUN_DIR}")

# Mapping rules:
# key = glob pattern to match incoming file(s) in UPLOAD_DIR
# value = target filename template (use placeholders {YYYYMMDD}, {DDMONYYYY}, {BATCH})
#
# if you want "keep as is", omit it from mapping and the script will leave it alone.
RENAME_TEMPLATES: Dict[str, str] = {
    # rename fo_secban_23082024.csv -> fo_secban_23-Aug-2024.csv
    "fo_secban_*.csv": "fo_secban_{DDMONYYYY}.csv",

    # BSE span rename example: BSERISK20240823-00.spn -> Nsocl.20240823.i01.spn
    "BSERISK*-00.spn": "Nsocl.{YYYYMMDD}.i01.spn",

    # Common text files -> DAT patterns used by doc examples
    "Security.txt": "NSM{YYYYMMDD}.{BATCH}.DAT",     # doc example shows NSMload*.23.DAT
    "Contract.txt": "DNSM{YYYYMMDD}.{BATCH}.DAT",
    "Spd_contract.txt": "SCM{YYYYMMDD}.{BATCH}.DAT",
    "SCRIP_*.TXT": "BSM{YYYYMMDD}.{BATCH}.DAT",

    # REG files
    # - REG_IND220824.csv -> use "as is" for NSE (so we don't map it)
    # - REG_IND22-0824.csv -> rename for BSE
    "REG_IND22-0824.csv": "REG_IND22-{YYYYMMDD}_{BATCH}.DAT",

    # default fallback if you want to rename cd_contract (here left as-is)
    # "cd_contract.txt": "cd_contract_{YYYYMMDD}.txt",
}

# Commands to run in sequence (the document's commands)
# Platform-specific command generation
def get_executable_path(exe_name):
    """Get the correct executable path for the current platform"""
    if IS_WINDOWS:
        return os.path.join(RUN_DIR, f"{exe_name}{EXECUTABLE_EXT}")
    else:
        return f"./{exe_name}"

def get_command(exe_name, args=""):
    """Generate platform-specific command"""
    if IS_WINDOWS:
        exe_path = get_executable_path(exe_name)
        return f'{CD_COMMAND} "{RUN_DIR}" && "{exe_path}" {args}'
    else:
        return f"{CD_COMMAND} {RUN_DIR} && ./{exe_name} {args}"

COMMANDS = [
    get_command("NSMload", YYYYMMDD),
    get_command("BulkDNSMload", YYYYMMDD),
    get_command("SCMload", YYYYMMDD),
    get_command("Scrip_Master", YYYYMMDD),
    get_command("NseRegIndicator", YYYYMMDD),
    get_command("BseRegIndicator", YYYYMMDD),
]

# ===========================
# INTERNAL - do not edit below
# ===========================

def setup_windows_environment():
    """Setup Windows development environment with mock executables"""
    if not IS_WINDOWS:
        return

    print("Setting up Windows development environment...")

    # Create mock batch files for testing
    executables = ["NSMload", "BulkDNSMload", "SCMload", "Scrip_Master", "NseRegIndicator", "BseRegIndicator"]

    for exe in executables:
        bat_file = os.path.join(RUN_DIR, f"{exe}.bat")
        if not os.path.exists(bat_file):
            with open(bat_file, 'w') as f:
                f.write(f"@echo off\n")
                f.write(f"echo [MOCK] Executing {exe} with arguments: %*\n")
                f.write(f"echo [MOCK] Date parameter: %1\n")
                f.write(f"echo [MOCK] {exe} completed successfully\n")
                f.write(f"timeout /t 1 /nobreak >nul\n")  # Small delay to simulate processing
            print(f"  Created mock executable: {bat_file}")

    # Create sample test files in upload directory
    test_files = [
        "fo_secban_23082024.csv",
        "BSERISK20240823-00.spn",
        "Security.txt",
        "Contract.txt",
        "Spd_contract.txt",
        "SCRIP_150623.TXT",
        "REG_IND22-0824.csv"
    ]

    for filename in test_files:
        file_path = os.path.join(UPLOAD_DIR, filename)
        if not os.path.exists(file_path):
            with open(file_path, 'w') as f:
                f.write(f"Test content for {filename}\n")
                f.write(f"Created: {datetime.datetime.now()}\n")
            print(f"  Created test file: {filename}")

    print("Windows environment setup complete!")
def make_target_name(tpl: str):
    return tpl.format(YYYYMMDD=YYYYMMDD, DDMONYYYY=DD_MON_YYYY, DDMONYYYY_UPPER=DD_MON_YYYY.upper(), BATCH=BATCH)

def find_matches(pattern: str):
    glob_path = os.path.join(UPLOAD_DIR, pattern)
    return sorted(glob.glob(glob_path))

def safe_move(src, dst, dry_run=False):
    if dry_run:
        print(f"[DRY] would move: {src} -> {dst}")
        return
    # remove existing dst if present (doc examples sometimes overwrite)
    if os.path.exists(dst):
        backup = dst + ".bak"
        print(f" - target exists: backing up {dst} -> {backup}")
        shutil.move(dst, backup)
    print(f" - moving {src} -> {dst}")
    shutil.move(src, dst)

def process_renames(dry_run=False):
    print("Checking upload directory:", UPLOAD_DIR)
    for pattern, tpl in RENAME_TEMPLATES.items():
        matches = find_matches(pattern)
        if not matches:
            print(f"Pattern '{pattern}': no files found.")
            continue
        for src_path in matches:
            filename = os.path.basename(src_path)
            target_name = make_target_name(tpl)
            dst_path = os.path.join(UPLOAD_DIR, target_name)
            if filename == target_name:
                print(f" - '{filename}' already named correctly, skipping.")
                continue
            print(f"Pattern '{pattern}': rename '{filename}' -> '{target_name}'")
            safe_move(src_path, dst_path, dry_run=dry_run)

def run_command_list(dry_run=False):
    for cmd in COMMANDS:
        print("\n>>> Running:", cmd)
        if dry_run:
            print("[DRY] would run command, skipping execution.")
            continue

        try:
            # Platform-specific execution
            if IS_WINDOWS:
                r = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=300)
            else:
                r = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=300)

            print(f"Return Code: {r.returncode}")

            if r.stdout:
                print("STDOUT:")
                print(r.stdout.strip())

            if r.stderr:
                print("STDERR:")
                print(r.stderr.strip())

            if r.returncode != 0:
                print(f"⚠️  Command failed with return code: {r.returncode}")
            else:
                print("✅ Command completed successfully")

        except subprocess.TimeoutExpired:
            print("⏰ Command timed out after 5 minutes")
        except Exception as e:
            print(f"💥 Error executing command: {e}")

def main():
    parser = argparse.ArgumentParser(description="Daily Process Automation for Exchange Data")
    parser.add_argument("--dry", action="store_true", help="dry-run (show actions, don't perform)")
    parser.add_argument("--setup", action="store_true", help="setup Windows development environment")
    args = parser.parse_args()

    print("=" * 60)
    print("Daily Process Automation")
    print("=" * 60)
    print(f"Platform: {platform.system()}")
    print(f"Upload Directory: {UPLOAD_DIR}")
    print(f"Run Directory: {RUN_DIR}")
    print(f"Date: {YYYYMMDD} ({DD_MON_YYYY})")
    print("=" * 60)

    # Setup Windows environment if requested
    if args.setup:
        setup_windows_environment()
        return

    # Auto-setup Windows environment if directories are empty
    if IS_WINDOWS and not os.listdir(RUN_DIR):
        print("Windows environment not set up. Setting up automatically...")
        setup_windows_environment()
        print()

    dry_run = args.dry
    if dry_run:
        print("🔍 DRY RUN MODE - No changes will be made")
        print()

    # Process file renames
    process_renames(dry_run=dry_run)

    # Execute commands
    print("\nAll renames processed. Now executing commands.")
    run_command_list(dry_run=dry_run)

    print("\n" + "=" * 60)
    print("✅ Daily process completed!")
    print("=" * 60)

if __name__ == "__main__":
    main()

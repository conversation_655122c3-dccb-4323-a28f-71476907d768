#!/usr/bin/env python3
"""
Network connectivity test for ************
"""

import subprocess
import socket
import time
from datetime import datetime

def test_ping(target_ip, count=4, timeout=5):
    """Test ping connectivity"""
    print(f"🏓 Testing ping to {target_ip}...")
    
    try:
        result = subprocess.run(
            ["ping", "-n", str(count), target_ip], 
            capture_output=True, 
            text=True, 
            timeout=timeout
        )
        
        if result.returncode == 0:
            print(f"  ✅ Ping successful!")
            # Extract statistics
            lines = result.stdout.split('\n')
            for line in lines:
                if 'Packets:' in line or 'Average' in line:
                    print(f"    {line.strip()}")
            return True
        else:
            print(f"  ❌ Ping failed (return code: {result.returncode})")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"  ⏰ Ping timed out after {timeout} seconds")
        return False
    except Exception as e:
        print(f"  💥 Ping error: {e}")
        return False

def test_socket_connection(target_ip, ports):
    """Test socket connectivity to specific ports"""
    print(f"🔌 Testing socket connections to {target_ip}...")
    
    results = {}
    
    for port in ports:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            
            start_time = time.time()
            result = sock.connect_ex((target_ip, port))
            end_time = time.time()
            
            if result == 0:
                print(f"  ✅ Port {port} - Open ({(end_time - start_time)*1000:.1f}ms)")
                results[port] = True
            else:
                print(f"  ❌ Port {port} - Closed/Filtered")
                results[port] = False
                
            sock.close()
            
        except Exception as e:
            print(f"  💥 Port {port} - Error: {e}")
            results[port] = False
    
    return results

def test_powershell_connectivity(target_ip, ports):
    """Test connectivity using PowerShell Test-NetConnection"""
    print(f"⚡ Testing PowerShell connectivity to {target_ip}...")
    
    for port in ports:
        try:
            cmd = f"Test-NetConnection -ComputerName {target_ip} -Port {port} -InformationLevel Quiet"
            result = subprocess.run(
                ["powershell", "-Command", cmd],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.stdout.strip().lower() == "true":
                print(f"  ✅ Port {port} - Reachable")
            else:
                print(f"  ❌ Port {port} - Not reachable")
                
        except subprocess.TimeoutExpired:
            print(f"  ⏰ Port {port} - Timeout")
        except Exception as e:
            print(f"  💥 Port {port} - Error: {e}")

def test_traceroute(target_ip):
    """Test traceroute to target"""
    print(f"🗺️  Testing traceroute to {target_ip}...")
    
    try:
        result = subprocess.run(
            ["tracert", "-h", "10", target_ip], 
            capture_output=True, 
            text=True, 
            timeout=30
        )
        
        if result.returncode == 0:
            print("  ✅ Traceroute completed:")
            lines = result.stdout.split('\n')
            for i, line in enumerate(lines[:15]):  # Show first 15 lines
                if line.strip() and not line.startswith('Tracing'):
                    print(f"    {line.strip()}")
        else:
            print(f"  ❌ Traceroute failed")
            
    except subprocess.TimeoutExpired:
        print(f"  ⏰ Traceroute timed out")
    except Exception as e:
        print(f"  💥 Traceroute error: {e}")

def main():
    """Main test function"""
    target_ip = "************"
    common_ports = [22, 80, 443, 3389, 8080, 21, 23, 25, 53, 110, 143, 993, 995]
    
    print("Network Connectivity Test")
    print("=" * 50)
    print(f"Target: {target_ip}")
    print(f"Time: {datetime.now()}")
    print("=" * 50)
    
    # Test 1: Basic ping
    ping_success = test_ping(target_ip, count=4, timeout=10)
    print()
    
    # Test 2: Socket connections
    socket_results = test_socket_connection(target_ip, common_ports[:5])  # Test first 5 ports
    print()
    
    # Test 3: PowerShell connectivity (if ping failed)
    if not ping_success:
        test_powershell_connectivity(target_ip, [80, 443, 22, 3389])
        print()
    
    # Test 4: Traceroute (if ping failed)
    if not ping_success:
        test_traceroute(target_ip)
        print()
    
    # Summary
    print("=" * 50)
    print("Summary:")
    print(f"  Ping: {'✅ Success' if ping_success else '❌ Failed'}")
    
    open_ports = [port for port, status in socket_results.items() if status]
    if open_ports:
        print(f"  Open ports: {', '.join(map(str, open_ports))}")
    else:
        print("  Open ports: None detected")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
